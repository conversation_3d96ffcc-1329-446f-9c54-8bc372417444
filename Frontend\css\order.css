 :root {
        --primary-color: #223a66;
        --secondary-color: #e12454;
        --white-color: #fff;
        --light-color: #f8f9fa;
        --gray-color: #e6e6e6;
        --text-color: #555;
        --border-color: #eaeaea;
        --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    body {
        font-family: 'Poppins', sans-serif;
        background-color: #f5f7fa;
        color: var(--text-color);
        line-height: 1.6;
    }

    .order-history {
        max-width: 1200px;
        margin: 40px auto;
        padding: 30px;
        background-color: var(--white-color);
        border-radius: 15px;
        box-shadow: var(--box-shadow);
    }

    .order-history h2 {
        color: var(--primary-color);
        margin-bottom: 25px;
        font-size: 32px;
        font-weight: 700;
        text-align: center;
        position: relative;
        padding-bottom: 15px;
    }

    .order-history h2:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: var(--secondary-color);
    }

    .search-form {
        margin-bottom: 30px;
    }

    .search-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        max-width: 500px;
        margin: 0 auto;
        position: relative;
    }

    .search-bar input {
        padding: 14px 45px 14px 20px;
        border: 1px solid var(--border-color);
        border-radius: 50px;
        width: 100%;
        font-size: 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .search-bar input:focus {
        outline: none;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
    }

    .search-icon {
        margin-top: 5px;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--secondary-color);
        font-size: 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 20px;
        width: 20px;
        line-height: 1;
    }

    .search-icon:hover {
        color: var(--primary-color);
    }



    .status-filter {
        margin-bottom: 30px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
    }

    .status-filter .filter-btn {
        padding: 10px 20px;
        border: 1px solid var(--border-color);
        border-radius: 50px;
        background-color: var(--white-color);
        color: var(--text-color);
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .status-filter .filter-btn:hover {
        background-color: rgba(34, 58, 102, 0.05);
        border-color: var(--primary-color);
        color: var(--primary-color);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    }

    .status-filter .filter-btn.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
        box-shadow: 0 3px 8px rgba(34, 58, 102, 0.3);
    }

    .order-item {
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        background-color: var(--white-color);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .order-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-3px);
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
    }

    .order-id {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 16px;
    }

    .order-status {
        font-weight: 600;
        font-size: 14px;
        padding: 5px 12px;
        border-radius: 50px;
    }

    .order-date {
        font-size: 14px;
        color: var(--text-color);
    }

    .delete-order {
        color: var(--secondary-color);
        background-color: var(--white-color);
        font-size: 14px;
        text-decoration: none;
        font-weight: 500;
        padding: 6px 12px;
        border-radius: 5px;
        border: 1px solid var(--secondary-color);
        transition: all 0.3s ease;
    }

    .delete-order:hover {
        background-color: var(--secondary-color);
        color: var(--white-color);
    }

    .order-products {
        background-color: var(--light-color);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .order-products table.no-header-no-border {
        width: 100%;
        border-collapse: collapse;
    }

    .order-products table.no-header-no-border thead {
        display: none;
    }

    .order-products table.no-header-no-border,
    .order-products table.no-header-no-border th,
    .order-products table.no-header-no-border td {
        border: none;
    }

    .order-products table.no-header-no-border td {
        padding: 12px 8px;
        text-align: left;
        font-size: 14px;
        vertical-align: middle;
    }

    .order-products img {
        width: 70px;
        height: 70px;
        object-fit: cover;
        border-radius: 8px;
        margin-right: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
    }

    .order-footer .total {
        font-weight: 700;
        color: var(--secondary-color);
        font-size: 18px;
    }

    .order-details {
        display: none;
        margin-top: 25px;
        padding: 25px;
        background-color: #f8f9fc;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    }

    .order-details h4 {
        margin: 0 0 20px;
        color: var(--primary-color);
        font-size: 20px;
        font-weight: 600;
        position: relative;
        padding-bottom: 10px;
    }

    .order-details h4:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 2px;
        background-color: var(--secondary-color);
    }

    .order-details p {
        margin: 10px 0;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .order-details p strong {
        min-width: 180px;
        color: var(--primary-color);
        font-weight: 600;
    }

    .order-details table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .order-details th,
    .order-details td {
        padding: 12px 15px;
        border-bottom: 1px solid var(--border-color);
        text-align: left;
        font-size: 14px;
    }

    .order-details th {
        background-color: var(--primary-color);
        color: var(--white-color);
        font-weight: 500;
    }

    .order-details tr:last-child td {
        border-bottom: none;
    }

    .order-details tr:nth-child(even) {
        background-color: rgba(34, 58, 102, 0.03);
    }

    .order-details img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .toggle-details {
        color: var(--primary-color);
        cursor: pointer;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600;
        padding: 8px 20px;
        border: 1px solid var(--primary-color);
        border-radius: 50px;
        transition: all 0.3s ease;
    }

    .toggle-details:hover {
        background-color: var(--primary-color);
        color: var(--white-color);
    }

    .no-orders {
        text-align: center;
        color: var(--text-color);
        font-size: 16px;
        padding: 30px;
        background-color: var(--light-color);
        border-radius: 10px;
        margin: 20px 0;
    }

    .pagination-nav {
        margin-top: 30px;
        display: flex;
        justify-content: center;
    }

    .pagination-nav ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        gap: 10px;
    }

    .pagination-nav li {
        display: inline-block;
    }

    .pagination-nav a {
        display: block;
        padding: 10px 16px;
        border: 1px solid var(--border-color);
        border-radius: 50px;
        color: var(--primary-color);
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        background-color: var(--white-color);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .pagination-nav a:hover {
        background-color: rgba(34, 58, 102, 0.05);
        border-color: var(--primary-color);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    }

    .pagination-nav a.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
        box-shadow: 0 3px 8px rgba(34, 58, 102, 0.3);
    }

    .text-success {
        color: #28a745;
        text-align: center;
        padding: 15px;
        background-color: rgba(40, 167, 69, 0.1);
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .text-danger {
        color: var(--secondary-color);
        text-align: center;
        padding: 15px;
        background-color: rgba(225, 36, 84, 0.1);
        border-radius: 10px;
        margin-bottom: 20px;
    }

    /* Màu sắc trạng thái đơn hàng */
    .order-status[style*="orange"] {
        background-color: rgba(255, 152, 0, 0.1);
        color: #ff9800;
    }

    .order-status[style*="blue"] {
        background-color: rgba(34, 58, 102, 0.1);
        color: var(--primary-color);
    }

    .order-status[style*="red"] {
        background-color: rgba(225, 36, 84, 0.1);
        color: var(--secondary-color);
    }

    .order-status[style*="green"] {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    /* Điều chỉnh responsive */
    @media (max-width: 768px) {
        .order-history {
            padding: 20px 15px;
            margin: 20px 10px;
        }

        .order-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .status-filter {
            flex-wrap: wrap;
        }

        .order-details p strong {
            min-width: 120px;
        }
    }