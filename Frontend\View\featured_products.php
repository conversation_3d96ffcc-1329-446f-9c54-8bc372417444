<?php
require_once './model/dcFrontend.php';
?>

<section class="section service-2">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-7 text-center">
                <div class="section-title">
                    <h2>Sản Phẩm Nổi Bật</h2>
                    <div class="divider mx-auto my-4"></div>
                    <p>Khám phá các sản phẩm bán chạy nhất của chúng tôi.</p>
                </div>
            </div>
        </div>

        <div class="row product-grid">
            <?php
            $products = getFeaturedProducts(); // Lấy 9 sản phẩm bán chạy nhất từ database
            if (empty($products)) {
                echo "<div class='col-12'><p class='text-center'>Không có sản phẩm nổi bật nào.</p></div>";
            } else {
                foreach ($products as $product) {
                    ?>
                    <div class="col-lg-4 col-md-6 col-sm-6">
                        <div class="product-card mb-4">
                            <div class="product-image">
                                <img src="<?php echo htmlspecialchars($product['HinhAnh']); ?>"
                                     alt="<?php echo htmlspecialchars($product['TenSanPham']); ?>"
                                     class="img-fluid">
                            </div>
                            <div class="product-details p-3">
                                <div class="product-title-wrap">
                                    <h4 class="product-title"><?php echo htmlspecialchars($product['TenSanPham']); ?></h4>
                                </div>
                                <p class="price text-center mb-3"><?php echo number_format($product['Gia'], 0, ',', '.'); ?> VNĐ</p>
                                <div class="product-actions d-flex flex-column gap-2">
                                    <a href="./chitietsanpham.php?id=<?php echo htmlspecialchars($product['MaSanPham']); ?>"
                                       class="btn btn-outline-primary btn-sm w-100">Xem chi tiết</a>
                                    <button onclick="addToCart('<?php echo htmlspecialchars($product['MaSanPham']); ?>')"
                                            class="btn btn-main-2 btn-sm w-100">
                                        <i class="icofont-shopping-cart"></i> Mua
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
            ?>
        </div>

            <!-- Thêm liên kết "View all" -->
            <div class="row justify-content-center">
                <div class="col-lg-7 text-center">
                    <a href="./all_thuoc.php" class="btn btn-main btn-round-full">View all</a>
                </div>
            </div>
        </div>
    </div>

</section>

   <!-- Thêm section benefits với background image -->
<section class="benefits">
    <div class="benefits-inner"></div>
</section>

<!--  -->

<!-- Thêm JavaScript để xử lý nút "Mua" -->
<script src="./js/addcart.js"></script>

