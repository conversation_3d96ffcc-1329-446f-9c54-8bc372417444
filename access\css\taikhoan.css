
.content-item{
    min-height: 280px;
    position: relative;
}
.account-table {
    width: 100%;
    border-color: white;
    /* border: #796f6f; */
}

.table-header {
    background-color: #f2f2f2;
}
.table-container {
    background-color: #ddd;
    max-height: 500px;
    overflow-y: auto;
    position: relative; /* Set position to relative */
}

.account-table {
    border-collapse: collapse;
    width: 100%;
}

.account-table th {
    /* box-shadow: 0 0 10px rgba(44, 5, 5, 0.9);    */
    background-color: #000000;
    border-bottom: 1px solid black;
    color: rgb(255, 255, 255);
    position: sticky; /* Set position to sticky */
    top: 0; /* Stick to the top */
    z-index: 2; /* Ensure the header is above the content */
}

.account-table th,
.account-table td {
    padding: 16px;
    text-align: left;
}

/* .account-table td {
   border-bottom:1px solid #000000;
} */
.account-table tr{
    width: 100%;
}
.account-table tr:nth-child(odd) td {
    /* background-color:#3d4953; Odd row background color */
    /* color: #999;Even row text color */
    /* Odd row text color */
    background-color:whitesmoke;
    border-bottom: 1px solid #ddd;
    color: #000000;
    /* border-bottom: 1px solid black; */
    height: 25px;
}

.account-table tr:nth-child(even) td {
    background-color:whitesmoke; /* Even row background color */
    color:#0e0d0d; /* Even row text color */
    /* border-bottom: 1px solid black; */
    border-bottom: 1px solid #ddd;
    height: 25px;
}

.user-id, .username, .password, .email, .role {
    width: 300px; /* Adjust as needed */
}
.action-buttons{
    box-shadow: 0 0 10px rgba(20, 19, 19, 0.692);   
    display: flex;
    align-items: center;
    height: 60px;
}

.btn-add,
.btn-xuat{
    margin-right: 15px;
    padding: 10px 18px;
    cursor: pointer;
    border: none;
    font-size: 14px;
    text-align: center;
    margin-left: 20px;
    width: 150px;
}
.btn-search{
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
}

.btn-add {
    background-color:#000000;
    color: #fff;
}
.action-buttons a:hover button{
    background-color: #28a745;
}
/* .btn-add :hover{
    background-color: #28a745;
} */

.btn-xuat {
    background-color:#000000;
    color: #fff;
}
.btn-xuat:hover{
    background-color: #007bff;
}

.btn-search {
    background-color:white;
    color: #141313;
}
.btn-search {
    margin-right: 15px;
    /* margin-left: 5px; */
    padding: 10px 18px;
    cursor: pointer;
    border: none;
    font-size: 14px;
    text-align: center;
}
input[type="text"]{
    padding: 10px;
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
    border-color: 2px solid black;
}
input[type="date"] {
    padding: 10px;
    border-color: 1px solid black;
    background-color: #302e2e;
    color: #ddd;
}
/* Optional: Style the font-awesome icons */
i.fas {
    margin-right: 5px;
}

/* Optional: Style the form elements */
form {
    margin-right: 10px;
    
}




/* Form con */
.add-form ,.delete-form,.update-form{
    display: none;
    position: absolute;
    top:50%;
    left: 40%;
    transform: translate(-50%, -50%);
    width: 2000px;
    border: 1px solid #e4d4d4;
    border-radius: 2px;
    background-color: #ccc;
    height: 800px;
    background-color: rgba(255, 254, 254,0.5);
   
    
}
.add-form-boder{
    width: 400px;
    margin: 30px auto;
    text-align: center;
    background-color: #ccc;
    box-shadow: 0 0 10px rgba(255, 254, 254, 1);
    border-radius: 3px;
    padding: 20px;
    
}
.add-form label ,.delete-form label,.update-form label{
    display: block;
    margin-bottom: 8px;
}

.add-form input, .add-form #LoaiDichVu ,.delete-form input ,.update-form input{
    margin-top: 10px;
    width:90% ;
    padding: 15px;
    margin-bottom: 30px; 
    box-sizing: border-box;
    border: 1px solid #ccc; /* Added border to input *//* Remove top border */
    border-radius: 5px;

}

.add-form button  ,.delete-form button,.update-form button{
    background-color: #4CAF50;
    color: white;
    padding: 10px 62px;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    margin: 1px auto;
    
}

.add-form button:hover{
    background-color:#999;

}
.thongke{
    display: flex;
}

.hanhdong a{
    margin: 0 10px;
    color: black;
}
.hanhdong a:hover{
    color: rgb(22, 22, 223);
    transform: calc(112, 112, 226,1.1);
}

/* update ------------------------------------------------------------------------------------------*/
.update-form-dv {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* Background overlay */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

.dv-update {
    background-color: #fff; /* Form background color */
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); /* Box shadow for a subtle effect */
}

.dv-update label {
    display: block;
    margin-bottom: 8px;
}

.dv-update input,
.dv-update textarea,
.dv-update #LoaiDichVu 
{
    width: 100%;
    padding: 8px;
    margin-bottom: 16px;
    box-sizing: border-box;
}

.dv-update button {
    background-color: #4caf50; /* Button background color */
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.dv-update button.btn-close {
    background-color: #e74c3c; /* Close button background color */
    margin-left: 10px;
}

/* endupdate -----------------------------------------------------------------------------------------------*/



/* CSS để quy định kích thước ảnh cho một lớp cụ thể */
.anh {
    position: relative;
    width: 15%;
}

.anh .img-hover {
    display: none;
    position: absolute;
    top: 25%;
    left: 70%;
    z-index: 1000000;
    width: 400px;
    height: 150px;
    border-radius: 2px;
    border: 1px solid rgb(107, 101, 101);
}

.anh:hover .img-hover {
    display: block;
}

.anh:hover .filename {
    display: none;
}


.fa-wrench ,.fa-trash-alt,.fa-check {
    border: 2px solid #585858; 
    border-radius: 50%;
    padding: 5px; 
}
.fa-wrench:hover i,.fa-trash-alt:hover i,.fa-times:hover i,.fa-check:hover i {
    background-color: red;
    color: #007bff;
    
}


.hanhdong a {
    position: relative;
    display: inline-block;
  }

  .fa-times:hover::before {
    content: "Hủy đơn";
    position: absolute;
    display: block;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.8); /* Màu nền của phần chữ */
    padding: 5px;
    border-radius: 2px;
    width: 100px; /* Điều chỉnh chiều rộng của phần chữ */
    top: -25px; /* Điều chỉnh vị trí nếu muốn hiển thị bên trên */
    left: 50%;
    transform: translateX(-50%);
  }