<?php
ob_start();
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
// Kiểm tra đăng nhập dựa trên username
if (!isset($_SESSION['username'])) {
    header("Location: ./View/signin.php");
    exit();
}

if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: ./View/signin.php");
    exit();
}
// ..................
$userId = null;
if (isset($_SESSION['username'])) {
    $userId = getUserIDByUsername($_SESSION['username']);
}
// Khởi tạo biến $cartCount
$cartCount = 0; // Giá trị mặc định
  // Nếu UserID tồn tại, lấy số lượng sản phẩm trong giỏ hàng
if (isset($userId) && $userId !== null) {
    $cartCount = getCartCount($userId);
}

// Fetch service categories for dropdown menu
$headerServiceCategories = [];
try {
    // Use the existing connection function
    $headerConn = connectDB();
    $headerQuery = "SELECT MaLoaiDichVu, TenLoaiDichVu FROM LoaiDichVu";
    $headerResult = $headerConn->query($headerQuery);
    if ($headerResult) {
        while ($headerRow = $headerResult->fetch_assoc()) {
            $headerServiceCategories[] = $headerRow;
        }
    }
    // Đóng kết nối sau khi sử dụng
    if (isset($headerConn)) {
        $headerConn->close();
    }
} catch (Exception $e) {
    // Handle error silently
}
?>

<!DOCTYPE html>
<html lang="zxx">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="description" content="Orbitor,business,company,agency,modern,bootstrap4,tech,software">
  <meta name="author" content="themefisher.com">

  <title>Health & Care </title>

  <!-- Favicon -->
  <link rel="shortcut icon" type="image/x-icon" href="/images/favicon.ico" />

  <!-- bootstrap.min css -->
  <link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">
  <!-- Icon Font Css -->
  <link rel="stylesheet" href="plugins/icofont/icofont.min.css">
  <!-- Font awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Slick Slider CSS -->
  <link rel="stylesheet" href="plugins/slick-carousel/slick/slick.css">
  <link rel="stylesheet" href="plugins/slick-carousel/slick/slick-theme.css">
  <!-- form nhập -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Main Stylesheet -->
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="css/stylesuggestion.css">
  <link rel="stylesheet" href="css/styleappointment.css">
  <link rel="stylesheet" href="css/custom-header.css">
  <link rel="stylesheet" href="css/medicine.css">
  <link rel="stylesheet" href="css/cart.css">
  <link rel="stylesheet" href="css/order.css">
</head>
<body id="top">

<header>
<div id="notification" class="notification" style="display: none;">
    <span id="notification-message"></span>
</div>
	<div class="header-top-bar">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-6">
					<ul class="top-bar-info list-inline-item pl-0 mb-0">
						<li class="list-inline-item"><a href="mailto:<EMAIL>"><i class="icofont-support-faq mr-2"></i><EMAIL></a></li>
						<li class="list-inline-item"><i class="icofont-location-pin mr-2"></i>Address: P. Trịnh Văn Bô, Hà Nội</li>
					</ul>
				</div>
				<div class="col-lg-6">
					<div class="text-lg-right top-right-bar mt-2 mt-lg-0">
						<a href="tel:+84 868366503">
							<span class="h4"><i class="icofont-phone"></i> 868-366-503</span>
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</header>
<nav class="navbar navbar-expand-lg navigation" id="navbar">
	<div class="container">
		<a class="navbar-brand" href="./indexx.php">
			<img src="images/logo.png" alt="" class="img-fluid">
		</a>

		<button class="navbar-toggler collapsed" type="button" data-toggle="collapse" data-target="#navbarmain" aria-controls="navbarmain" aria-expanded="false" aria-label="Toggle navigation">
			<span class="icofont-navigation-menu"></span>
		</button>

		<div class="collapse navbar-collapse" id="navbarmain">
			<ul class="navbar-nav ml-auto">
				<li class="nav-item active">
					<a class="nav-link" href="./indexx.php"><i class="icofont-ui-home"></i>Trang chủ</a>
				</li>

				<li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" href="./service.php" id="dropdown02" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Dịch vụ<i class="icofont-thin-down"></i></a>
					<ul class="dropdown-menu" aria-labelledby="dropdown02">
						<li><a class="dropdown-item" href="./service.php">Tất cả dịch vụ</a></li>
						<?php foreach ($headerServiceCategories as $headerService): ?>
						<li><a class="dropdown-item" href="./service-single.php?id=<?php echo htmlspecialchars($headerService['MaLoaiDichVu']); ?>"><?php echo htmlspecialchars($headerService['TenLoaiDichVu']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				</li>

				<li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" href="./all_thuoc.php" id="dropdown02" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Sản phẩm<i class="icofont-thin-down"></i></a>
					<ul class="dropdown-menu" aria-labelledby="dropdown02">
						<li><a class="dropdown-item" href="./thuoc_kedon.php">Thuốc kê đơn</a></li>
						<li><a class="dropdown-item" href="./thuoc_khongdon.php">Thuốc không kê đơn</a></li>
					</ul>
				</li>

				<li class="nav-item"><a class="nav-link" href="./appoinment.php">Đặt lịch</a></li>

				<li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" href="blog-sidebar.html" id="dropdown05" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Blog <i class="icofont-thin-down"></i></a>
					<ul class="dropdown-menu" aria-labelledby="dropdown05">
						<li><a class="dropdown-item" href="./goi_y_suc_khoe.php">Blog with Sidebar</a></li>
						<li><a class="dropdown-item" href="blog-single.html">Blog Single</a></li>
					</ul>
				</li>
				<!-- <li class="nav-item"><a class="nav-link" href="about.html">About</a></li> -->
				<li class="nav-item"><a class="nav-link" href="./contact.php">Liên hệ</a></li>
				<?php if (isset($_SESSION['username'])) : ?>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle account-button" href="#" id="dropdown05" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i class="icofont-user-alt-5"></i>
							<span><?php echo htmlspecialchars($_SESSION['username']); ?></span>
						</a>
						<ul class="dropdown-menu" aria-labelledby="dropdown05">
							<li><a class="dropdown-item" href="./user_profile.php">Thông tin cá nhân</a></li>
							<li><a class="dropdown-item" href="./order_history.php">Xem đơn hàng</a></li>
							<li><a class="dropdown-item" href="./user_appoiment.php">Xem đơn đặt lịch</a></li>
							<li><a class="dropdown-item" href="?logout=1">Đăng Xuất</a></li>
						</ul>
					</li>
				<?php else : ?>
					<li class="nav-item">
						<a class="nav-link account-button" href="./View/signin.php">
							<i class="icofont-user-alt-5"></i>
							<span>Đăng nhập/Đăng ký</span>
						</a>
					</li>
				<?php endif; ?>

				<!-- giohang -->
				<li class="nav-item">
					<a class="nav-link cart-button" href="./cart.php">
						<i class="icofont-cart"></i>
						<span>Giỏ hàng</span>
						<span id="cart-count"><?php echo $cartCount; ?></span>
					</a>
				</li>
			</ul>
		</div>
	</div>
</nav>

<script>
    // Xử lý cuộn trang
    window.addEventListener('scroll', function() {
        const headerTopBar = document.querySelector('.header-top-bar');
        const navbar = document.querySelector('.navbar');
        const headerTopBarHeight = headerTopBar.offsetHeight;

        if (window.scrollY > headerTopBarHeight) { // Ẩn khi cuộn qua chiều cao của header-top-bar
            headerTopBar.classList.add('hidden');
            navbar.classList.add('fixed');
            document.body.style.paddingTop = navbar.offsetHeight + 'px'; // Thêm padding-top cho body
        } else {
            headerTopBar.classList.remove('hidden');
            navbar.classList.remove('fixed');
            document.body.style.paddingTop = '0'; // Xóa padding-top khi cuộn lên trên
        }
    });

    // Xác định trang hiện tại và thêm class active
    document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('#navbarmain .nav-item');

        navLinks.forEach(function(navItem) {
            const link = navItem.querySelector('.nav-link');
            if (!link) return;

            const href = link.getAttribute('href');
            if (!href) return;

            // Xử lý đường dẫn tương đối
            const hrefPath = href.startsWith('./') ? href.substring(2) : href;
            const currentPageName = currentPath.split('/').pop();

            // Kiểm tra nếu đường dẫn hiện tại khớp với href của liên kết
            if (currentPageName === hrefPath ||
                (currentPageName === '' && hrefPath === 'indexx.php') ||
                (currentPageName && hrefPath && currentPageName.includes(hrefPath))) {
                navItem.classList.add('active');
            } else {
                navItem.classList.remove('active');
            }
        });
    });
</script>
