<?php
session_start();
require_once './model/dcFrontend.php';

// Lấy UserID từ username
$userId = null;
if (isset($_SESSION['username'])) {
    $userId = getUserIDByUsername($_SESSION['username']);
}

include './View/header.php';

// Lấy <PERSON> từ URL
$maSanPham = isset($_GET['id']) ? $_GET['id'] : '';
$product = getProductById($maSanPham);

if (!$product) {
    echo "<p class='text-center'>Sản phẩm không tồn tại.</p>";
    include './View/footer.php';
    exit;
}
?>

<section class="page-title bg-1">
  <div class="overlay"></div>
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="block text-center">
          <h1 class="text-capitalize mb-5 text-lg">Chi Tiết Sản <PERSON>ẩm</h1>
          <ul class="list-inline breadcumb-nav">
            <li class="list-inline-item"><a href="./indexx.php" class="text-white">Trang Chủ</a></li>
            <li class="list-inline-item"><span class="text-white">/</span></li>
            <li class="list-inline-item"><span class="text-white-50">Chi Tiết Sản Phẩm</span></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Section Chi tiết sản phẩm -->
<section class="section product-detail">
    <div class="container">
        <div class="row align-items-center">
            <!-- Hình ảnh sản phẩm -->
            <div class="col-lg-6 col-md-6 mb-4 mb-lg-0">
                <div class="product-image-wrapper">
                    <div class="product-image">
                        <img src="<?php echo htmlspecialchars($product['HinhAnh']); ?>" alt="<?php echo htmlspecialchars($product['TenSanPham']); ?>" class="img-fluid">
                    </div>
                </div>
            </div>

            <!-- Thông tin sản phẩm -->
            <div class="col-lg-6 col-md-6">
                <div class="product-info">
                    <div class="product-header">
                        <h1 class="product-title"><?php echo htmlspecialchars($product['TenSanPham']); ?></h1>
                        <div class="price-wrapper">
                            <span class="price"><?php echo number_format($product['Gia'], 0, ',', '.'); ?> VNĐ</span>
                        </div>
                    </div>

                    <div class="product-details">
                        <div class="detail-item">
                            <span class="detail-label">Đối tượng sử dụng:</span>
                            <span class="detail-value">
                                <?php
                                switch ($product['DoiTuong']) {
                                    case 'TreEm':
                                        echo 'Trẻ em';
                                        break;
                                    case 'NguoiLon':
                                        echo 'Người lớn';
                                        break;
                                    case 'CaHai':
                                        echo 'Cả hai';
                                        break;
                                    default:
                                        echo 'Không xác định';
                                }
                                ?>
                            </span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">Số lượng còn lại:</span>
                            <span class="detail-value stock-quantity"><?php echo htmlspecialchars($product['SoLuongTon']); ?> sản phẩm</span>
                        </div>
                    </div>

                    <!-- Form chọn số lượng -->
                    <div class="quantity-section">
                        <div class="quantity-wrapper">
                            <label for="quantity" class="quantity-label">Số lượng:</label>
                            <div class="quantity-input-wrapper">
                                <button type="button" class="quantity-btn minus" onclick="decreaseQuantity()">-</button>
                                <input type="number" id="quantity" name="quantity" min="1" max="<?php echo htmlspecialchars($product['SoLuongTon']); ?>" value="1" class="quantity-input">
                                <button type="button" class="quantity-btn plus" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>
                    </div>

                    <!-- Nút hành động -->
                    <div class="action-buttons">
                        <button type="button" id="buy-now" class="btn btn-primary btn-buy">
                            <i class="icofont-cart me-2"></i>Thêm vào giỏ hàng
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-back" onclick="history.back()">
                            <i class="icofont-arrow-left me-2"></i>Quay lại
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Phần thông tin chi tiết sản phẩm -->
        <div class="row product-details-section">
            <!-- Cột bên trái: Mô tả, Cách dùng, Thành phần -->
            <div class="col-lg-8 col-md-12">
                <!-- Navigation tabs -->
                <div class="product-tabs">
                    <ul class="nav nav-tabs" id="productTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab">
                                <i class="icofont-info-circle me-2"></i>Mô Tả
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="usage-tab" data-bs-toggle="tab" data-bs-target="#usage" type="button" role="tab">
                                <i class="icofont-pills me-2"></i>Cách Dùng
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ingredients-tab" data-bs-toggle="tab" data-bs-target="#ingredients" type="button" role="tab">
                                <i class="icofont-molecule me-2"></i>Thành Phần
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="productTabsContent">
                        <!-- Tab Mô tả -->
                        <div class="tab-pane fade show active" id="description" role="tabpanel">
                            <div class="content-section">
                                <?php
                                $moTa = htmlspecialchars($product['MoTa']);
                                $paragraphs = explode("\n", $moTa);
                                foreach ($paragraphs as $index => $paragraph) {
                                    if (trim($paragraph)) {
                                        $class = $index === 0 ? 'lead' : '';
                                        echo "<p class='$class'>" . trim($paragraph) . "</p>";
                                    }
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Tab Cách dùng -->
                        <div class="tab-pane fade" id="usage" role="tabpanel">
                            <div class="content-section">
                                <?php
                                $cachDung = htmlspecialchars($product['CachDung']);
                                $paragraphs = explode("\n", $cachDung);
                                foreach ($paragraphs as $index => $paragraph) {
                                    if (trim($paragraph)) {
                                        $class = $index === 0 ? 'lead' : '';
                                        echo "<p class='$class'>" . trim($paragraph) . "</p>";
                                    }
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Tab Thành phần -->
                        <div class="tab-pane fade" id="ingredients" role="tabpanel">
                            <div class="content-section">
                                <?php
                                $thanhPhan = htmlspecialchars($product['ThanhPhan']);
                                $paragraphs = explode("\n", $thanhPhan);
                                foreach ($paragraphs as $index => $paragraph) {
                                    if (trim($paragraph)) {
                                        $class = $index === 0 ? 'lead' : '';
                                        echo "<p class='$class'>" . trim($paragraph) . "</p>";
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cột bên phải: Lưu ý và thông tin bổ sung -->
            <div class="col-lg-4 col-md-12">
                <div class="sidebar-content">
                    <!-- Card Lưu ý -->
                    <div class="warning-card">
                        <div class="card-header">
                            <h4 class="warning-title">
                                <i class="icofont-warning-alt me-2"></i>Lưu Ý Quan Trọng
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="warning-section">
                                <h5 class="section-title">Chống Chỉ Định</h5>
                                <ul class="warning-list">
                                    <li>Quá mẫn với bất kỳ thành phần nào của sản phẩm</li>
                                    <li>Phụ nữ có thai và cho con bú</li>
                                    <li>Trẻ em dưới 12 tuổi</li>
                                </ul>
                            </div>

                            <div class="warning-section">
                                <h5 class="section-title">Thận Trọng Khi Sử Dụng</h5>
                                <p>Nên tham khảo ý kiến bác sĩ trước khi sử dụng, đặc biệt với người có tiền sử bệnh lý.</p>
                                <p>Ngừng sử dụng nếu có phản ứng bất thường và liên hệ ngay với cơ sở y tế.</p>
                            </div>

                            <div class="warning-section">
                                <h5 class="section-title">Bảo Quản</h5>
                                <p>Bảo quản nơi khô ráo, thoáng mát, tránh ánh sáng trực tiếp. Để xa tầm tay trẻ em.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Card thông tin liên hệ -->
                    <div class="contact-card">
                        <div class="card-header">
                            <h4 class="contact-title">
                                <i class="icofont-support-faq me-2"></i>Cần Hỗ Trợ?
                            </h4>
                        </div>
                        <div class="card-body">
                            <p>Liên hệ với chúng tôi để được tư vấn chi tiết về sản phẩm.</p>
                            <div class="contact-info">
                                <p><i class="icofont-phone me-2"></i>Hotline: 1900-xxxx</p>
                                <p><i class="icofont-email me-2"></i>Email: <EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<?php
include './View/footer.php';
?>

<style>
    /* General Styles */
    .product-detail {
        padding: 60px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Product Image Styles */
    .product-image-wrapper {
        position: relative;
        padding: 20px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .product-image-wrapper:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .product-image img {
        width: 100%;
        height: auto;
        border-radius: 15px;
        object-fit: cover;
        max-height: 400px;
    }

    /* Product Info Styles */
    .product-info {
        padding: 30px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        height: fit-content;
    }

    .product-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 20px;
        margin-bottom: 25px;
    }

    .product-title {
        font-size: 2.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 15px;
        line-height: 1.3;
    }

    .price-wrapper {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .price {
        font-size: 2rem;
        font-weight: 800;
        color: #e74c3c;
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Product Details */
    .product-details {
        margin-bottom: 30px;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
    }

    .detail-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 1rem;
    }

    .stock-quantity {
        color: #28a745;
        font-weight: 600;
    }

    /* Quantity Section */
    .quantity-section {
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 15px;
        border: 2px solid #e9ecef;
    }

    .quantity-wrapper {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .quantity-label {
        font-weight: 600;
        color: #495057;
        font-size: 1.1rem;
        margin: 0;
        min-width: 100px;
    }

    .quantity-input-wrapper {
        display: flex;
        align-items: center;
        background: white;
        border-radius: 10px;
        border: 2px solid #dee2e6;
        overflow: hidden;
    }

    .quantity-btn {
        background: #007bff;
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .quantity-btn:hover {
        background: #0056b3;
    }

    .quantity-input {
        border: none;
        width: 80px;
        height: 40px;
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        outline: none;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-buy {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 15px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        flex: 1;
        min-width: 200px;
    }

    .btn-buy:hover {
        background: linear-gradient(45deg, #218838, #1ea080);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .btn-back {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 15px 25px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
    }

    /* Product Details Section */
    .product-details-section {
        margin-top: 60px;
        gap: 30px;
    }

    /* Tabs Styles */
    .product-tabs {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .nav-tabs {
        border-bottom: none;
        background: #f8f9fa;
        padding: 0;
        margin: 0;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 0;
        padding: 20px 30px;
        font-weight: 600;
        color: #6c757d;
        background: transparent;
        transition: all 0.3s ease;
        position: relative;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
    }

    .nav-tabs .nav-link.active {
        background: white;
        color: #007bff;
        border-bottom: 3px solid #007bff;
    }

    .tab-content {
        padding: 40px;
        min-height: 300px;
    }

    .content-section p {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #495057;
        margin-bottom: 20px;
    }

    .content-section .lead {
        font-size: 1.3rem;
        font-weight: 500;
        color: #2c3e50;
    }

    /* Sidebar Content */
    .sidebar-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    /* Warning Card */
    .warning-card {
        background: linear-gradient(135deg, #fff3e1 0%, #ffe0b3 100%);
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(255, 152, 0, 0.2);
        overflow: hidden;
        border-left: 5px solid #ff9800;
    }

    .warning-card .card-header {
        background: linear-gradient(45deg, #ff9800, #f57c00);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .warning-title {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
    }

    .warning-card .card-body {
        padding: 25px;
    }

    .warning-section {
        margin-bottom: 25px;
    }

    .warning-section:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #d84315;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #ffcc80;
    }

    .warning-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .warning-list li {
        padding: 8px 0;
        padding-left: 25px;
        position: relative;
        color: #5d4037;
        font-weight: 500;
    }

    .warning-list li:before {
        content: "⚠";
        position: absolute;
        left: 0;
        color: #ff9800;
        font-weight: bold;
    }

    .warning-section p {
        color: #5d4037;
        font-weight: 500;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    /* Contact Card */
    .contact-card {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(33, 150, 243, 0.2);
        overflow: hidden;
        border-left: 5px solid #2196f3;
    }

    .contact-card .card-header {
        background: linear-gradient(45deg, #2196f3, #1976d2);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .contact-title {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
    }

    .contact-card .card-body {
        padding: 25px;
    }

    .contact-card p {
        color: #1565c0;
        font-weight: 500;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .contact-info p {
        margin-bottom: 10px;
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .product-title {
            font-size: 1.8rem;
        }

        .price {
            font-size: 1.6rem;
        }

        .quantity-wrapper {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn-buy, .btn-back {
            min-width: 100%;
        }

        .nav-tabs .nav-link {
            padding: 15px 20px;
            font-size: 0.9rem;
        }

        .tab-content {
            padding: 25px;
        }

        .product-details-section {
            margin-top: 40px;
        }
    }

    @media (max-width: 576px) {
        .product-detail {
            padding: 40px 0;
        }

        .product-info {
            padding: 20px;
        }

        .nav-tabs .nav-link {
            padding: 12px 15px;
            font-size: 0.85rem;
        }
    }
</style>
<script>
    // Quantity control functions
    function increaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);
        const maxValue = parseInt(quantityInput.max);

        if (currentValue < maxValue) {
            quantityInput.value = currentValue + 1;
        }
    }

    function decreaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);
        const minValue = parseInt(quantityInput.min);

        if (currentValue > minValue) {
            quantityInput.value = currentValue - 1;
        }
    }

    // Buy now functionality
    document.getElementById('buy-now').addEventListener('click', function(event) {
        event.preventDefault();
        const quantity = document.getElementById('quantity').value;
        const maSanPham = '<?php echo $maSanPham; ?>';

        // Show loading state
        const button = this;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="icofont-spinner-alt-3 icofont-spin me-2"></i>Đang thêm...';
        button.disabled = true;

        const formData = new FormData();
        formData.append('maSanPham', maSanPham);
        formData.append('soLuong', quantity);

        fetch('./model/add_to_cart.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message with better styling
                showNotification('Đã thêm sản phẩm vào giỏ hàng thành công!', 'success');

                // Update cart count
                fetch('./model/get_cart_count.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(countData => {
                    const cartCountElement = document.getElementById('cart-count');
                    if (cartCountElement) {
                        cartCountElement.textContent = countData.count;
                        // Add animation to cart icon
                        cartCountElement.parentElement.classList.add('cart-updated');
                        setTimeout(() => {
                            cartCountElement.parentElement.classList.remove('cart-updated');
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Lỗi lấy số lượng giỏ hàng:', error);
                });
            } else {
                showNotification(data.message || 'Có lỗi xảy ra khi thêm vào giỏ hàng.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Có lỗi xảy ra khi thêm vào giỏ hàng.', 'error');
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        });
    });

    // Notification function
    function showNotification(message, type) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.custom-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `custom-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="icofont-${type === 'success' ? 'check-circled' : 'warning-alt'} me-2"></i>
                ${message}
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize Bootstrap tabs if available
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tabs
        const triggerTabList = [].slice.call(document.querySelectorAll('#productTabs button'));
        triggerTabList.forEach(function (triggerEl) {
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault();

                // Remove active class from all tabs and content
                document.querySelectorAll('#productTabs .nav-link').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const target = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(target);
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }
            });
        });
    });
</script>

<!-- Additional CSS for notifications and animations -->
<style>
    .custom-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        z-index: 9999;
        transform: translateX(400px);
        transition: transform 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .custom-notification.success {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .custom-notification.error {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }

    .custom-notification.show {
        transform: translateX(0);
    }

    .cart-updated {
        animation: cartBounce 0.6s ease;
    }

    @keyframes cartBounce {
        0%, 20%, 60%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        80% {
            transform: translateY(-5px);
        }
    }

    .icofont-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>