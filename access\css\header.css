.top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #34495e;
    color: #ecf0f1;
    padding: 10px 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.icon-container {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.icon {
    position: relative;
    margin-right: 15px;
    font-size: 20px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.icon:hover {
    color: #1abc9c;
}

.badge {
    position: absolute;
    top: -5px;
    right: -10px;
    background-color: #e74c3c;
    color: #fff;
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 50%;
}

.user-info {
    display: flex;
    align-items: center;
    font-size: 16px;
    cursor: pointer;
}

.user-info i {
    font-size: 24px;
    margin-right: 10px;
}